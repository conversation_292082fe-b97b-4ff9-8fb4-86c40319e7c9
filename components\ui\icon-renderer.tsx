import {
  BookOpen,
  Package,
  Home,
  Settings,
  User,
  Download,
  CreditCard,
  FileText,
  Heart,
  Users,
  BarChart3,
  ShoppingCart,
  MessageSquare,
  Bell,
  Shield,
  Database,
  Globe,
  Zap,
  LayoutDashboard,
  Briefcase,
  BarChart,
  type LucideIcon,
} from "lucide-react"
import { cn } from "@/lib/utils"

// Static mapping of icon names to components
const ICON_MAP: Record<string, LucideIcon> = {
  home: Home,
  bookOpen: BookOpen,
  heart: Heart,
  package: Package,
  fileText: FileText,
  download: Download,
  user: User,
  creditCard: CreditCard,
  settings: Settings,
  users: Users,
  barChart3: BarChart3,
  shoppingCart: ShoppingCart,
  messageSquare: MessageSquare,
  bell: Bell,
  shield: Shield,
  database: Database,
  globe: Globe,
  zap: Zap,
  layoutDashboard: LayoutDashboard,
  briefcase: Briefcase,
  barChart: BarChart,
}

interface IconRendererProps {
  iconName: string
  className?: string
}

export function IconRenderer({ iconName, className }: IconRendererProps) {
  const IconComponent = ICON_MAP[iconName]
  
  if (!IconComponent) {
    // Fallback to a default icon if the specified icon is not found
    const DefaultIcon = Home
    return <DefaultIcon className={cn("h-5 w-5", className)} />
  }
  
  return <IconComponent className={cn("h-5 w-5", className)} />
}

// Helper function to get available icon names
export function getAvailableIcons(): string[] {
  return Object.keys(ICON_MAP)
}

// Type for icon names to ensure type safety
export type IconName = keyof typeof ICON_MAP
